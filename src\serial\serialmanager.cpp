#include "serialmanager.h"
#include <QMutexLocker>
#include "log.h"

SerialManager::SerialManager(QObject *parent)
    : QObject(parent)
    , m_serialDevice(nullptr)
    , m_atParser(nullptr)
    , m_reconnectTimer(nullptr)
    , m_autoReconnect(false)
    , m_reconnectInterval(5000)
    , m_isInitialized(false)
{
    m_serialDevice = new SerialDevice(this);
    m_atParser = new AtCommandParser(this);
    m_reconnectTimer = new QTimer(this);
    m_reconnectTimer->setSingleShot(true);

    // Connect serial device signals
    connect(m_serialDevice, &SerialDevice::dataReceived,
            this, &SerialManager::onSerialDataReceived);
    connect(m_serialDevice, &SerialDevice::connectionChanged,
            this, &SerialManager::onSerialConnectionChanged);
    connect(m_serialDevice, &SerialDevice::errorOccurred,
            this, &SerialManager::onSerialError);

    // Connect AT parser signals
    connect(m_atParser, &AtCommandParser::commandParsed,
            this, &SerialManager::onAtCommandParsed);
    connect(m_atParser, &AtCommandParser::responseParsed,
            this, &SerialManager::onAtResponseParsed);
    connect(m_atParser, &AtCommandParser::urcReceived,
            this, &SerialManager::onUrcReceived);
    connect(m_atParser, &AtCommandParser::parseError,
            this, &SerialManager::onAtParseError);

    // Connect reconnect timer
    connect(m_reconnectTimer, &QTimer::timeout,
            this, &SerialManager::onReconnectTimeout);
}

SerialManager::~SerialManager()
{
    stop();
}

bool SerialManager::initialize(const SerialConfig& config)
{
    QMutexLocker locker(&m_mutex);

    m_config = config;

    // Configure serial device
    bool success = m_serialDevice->setPortSettings(
        config.portName,
        config.baudRate,
        config.dataBits,
        config.parity,
        config.stopBits,
        config.flowControl
    );

    if (!success) {
        setLastError("Failed to set serial port parameters");
        return false;
    }

    // Set timeout values
    m_serialDevice->setTimeout(config.readTimeout, config.writeTimeout);

    // Configure AT parser
    m_atParser->setResponseTimeout(config.readTimeout);

    m_isInitialized = true;

    logInfo(QString("Serial manager initialized successfully: %1").arg(config.portName));
    return true;
}

bool SerialManager::start()
{
    QMutexLocker locker(&m_mutex);

    if (!m_isInitialized) {
        setLastError("Serial manager not initialized");
        return false;
    }

    if (m_serialDevice->isOpen()) {
        return true; // Already started
    }

    if (!m_serialDevice->open()) {
        setLastError(QString("Failed to open serial port: %1").arg(m_serialDevice->lastError()));

        // Enable auto reconnect
        if (m_autoReconnect) {
            m_reconnectTimer->start(m_reconnectInterval);
        }

        return false;
    }

    logInfo("Serial manager started successfully");
    return true;
}

void SerialManager::stop()
{
    QMutexLocker locker(&m_mutex);

    if (m_reconnectTimer && m_reconnectTimer->isActive()) {
        m_reconnectTimer->stop();
    }

    if (m_serialDevice && m_serialDevice->isOpen()) {
        m_serialDevice->close();
    }

    if (m_atParser) {
        m_atParser->clearBuffer();
    }

    logInfo("Serial manager stopped");
}

bool SerialManager::isConnected() const
{
    QMutexLocker locker(&m_mutex);
    return m_serialDevice && m_serialDevice->isOpen();
}

bool SerialManager::sendAtCommand(const AtCommand& command)
{
    QMutexLocker locker(&m_mutex);

    if (!isConnected()) {
        setLastError("Serial port not connected");
        return false;
    }

    if (!command.isValid()) {
        setLastError("Invalid AT command");
        return false;
    }

    QString formattedCommand = m_atParser->formatCommand(command);
    QByteArray data = formattedCommand.toUtf8();

    qint64 bytesWritten = m_serialDevice->write(data);
    if (bytesWritten == -1) {
        setLastError(QString("Failed to send AT command: %1").arg(m_serialDevice->lastError()));
        return false;
    }

    logInfo(QString("Sent AT command: %1").arg(command.format()));
    emit atCommandSent(command);
    emit rawDataSent(data);

    return true;
}

bool SerialManager::sendAtCommand(const QString& commandStr)
{
    AtCommand command = m_atParser->parseCommand(commandStr);
    if (!command.isValid()) {
        setLastError(QString("Failed to parse AT command: %1").arg(commandStr));
        return false;
    }

    return sendAtCommand(command);
}

bool SerialManager::sendRawData(const QByteArray& data)
{
    QMutexLocker locker(&m_mutex);

    if (!isConnected()) {
        setLastError("Serial port not connected");
        return false;
    }

    if (data.isEmpty()) {
        return true;
    }

    qint64 bytesWritten = m_serialDevice->write(data);
    if (bytesWritten == -1) {
        setLastError(QString("Failed to send raw data: %1").arg(m_serialDevice->lastError()));
        return false;
    }

    logDebug(QString("Sent raw data: %1 bytes: %2").arg(QString(data.toHex(' '))).arg(bytesWritten));
    emit rawDataSent(data);

    return true;
}

ISerialDevice* SerialManager::getSerialDevice() const
{
    return m_serialDevice;
}

AtCommandParser* SerialManager::getAtParser() const
{
    return m_atParser;
}

QString SerialManager::lastError() const
{
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void SerialManager::setAutoReconnect(bool enabled, int interval)
{
    QMutexLocker locker(&m_mutex);
    m_autoReconnect = enabled;
    m_reconnectInterval = interval;

    logInfo(QString("Auto reconnect setting: %1 interval: %2ms").arg(enabled ? "enabled" : "disabled").arg(interval));
}

QStringList SerialManager::getAvailablePorts()
{
    return SerialDevice::availablePorts();
}

void SerialManager::onSerialDataReceived(const QByteArray& data)
{
    // Pass received data to AT parser
    m_atParser->processReceivedData(data);

    // Emit raw data received signal
    emit rawDataReceived(data);
}

void SerialManager::onSerialConnectionChanged(bool connected)
{
    logInfo(QString("Serial connection status changed: %1").arg(connected ? "connected" : "disconnected"));

    if (!connected && m_autoReconnect) {
        // Start reconnect timer
        m_reconnectTimer->start(m_reconnectInterval);
    }

    emit connectionChanged(connected);
}

void SerialManager::onSerialError(const QString& error)
{
    setLastError(QString("Serial port error: %1").arg(error));
    logError(m_lastError);
    emit errorOccurred(m_lastError);
}

void SerialManager::onAtCommandParsed(const AtCommand& command)
{
    logDebug(QString("Parsed AT command: %1").arg(command.format()));
    // This is usually a command received from serial port, may need special handling
    // This situation is rare in bridge mode
}

void SerialManager::onAtResponseParsed(const AtResponse& response)
{
    logDebug(QString("Parsed AT response: %1").arg(response.format()));
    emit atResponseReceived(response);
}

void SerialManager::onUrcReceived(const AtResponse& response)
{
    logInfo(QString("Received URC: %1").arg(response.format()));
    emit urcReceived(response);
}

void SerialManager::onAtParseError(const QString& error)
{
    setLastError(QString("AT parse error: %1").arg(error));
    logWarnning(m_lastError);
    emit errorOccurred(m_lastError);
}

void SerialManager::onReconnectTimeout()
{
    logInfo("Attempting auto reconnect to serial port...");
    attemptReconnect();
}

void SerialManager::setLastError(const QString& error)
{
    m_lastError = error;
}

void SerialManager::attemptReconnect()
{
    if (!m_autoReconnect) {
        return;
    }

    if (m_serialDevice->isOpen()) {
        return; // Already connected
    }

    logInfo(QString("Attempting to reconnect serial port: %1").arg(m_config.portName));

    if (m_serialDevice->open()) {
        logInfo("Serial port reconnected successfully");
    } else {
        logWarnning(QString("Serial port reconnection failed: %1").arg(m_serialDevice->lastError()));
        // Continue reconnecting
        m_reconnectTimer->start(m_reconnectInterval);
    }
}
