#include "atcommand.h"
#include <QRegularExpression>
#include <QDebug>

AtCommand::AtCommand()
    : m_command(MqttAtCommand::Unknown)
    , m_type(AtCommandType::Unknown)
{
}

AtCommand::AtCommand(const QString& rawCommand)
    : m_command(MqttAtCommand::Unknown)
    , m_type(AtCommandType::Unknown)
{
    parse(rawCommand);
}

AtCommand::AtCommand(MqttAtCommand command, AtCommandType type)
    : m_command(command)
    , m_type(type)
{
}

bool AtCommand::parse(const QString& rawCommand)
{
    m_rawCommand = rawCommand.trimmed();
    m_parameters.clear();

    if (m_rawCommand.isEmpty()) {
        return false;
    }

    // 基础AT指令
    if (m_rawCommand.toUpper() == "AT") {
        m_command = MqttAtCommand::AT;
        m_type = AtCommandType::Basic;
        return true;
    }

    // 检查是否以AT+开头
    if (!m_rawCommand.startsWith("AT+", Qt::CaseInsensitive)) {
        return false;
    }

    // 移除AT+前缀
    QString commandPart = m_rawCommand.mid(3);

    // 查找等号或问号位置
    int equalPos = commandPart.indexOf('=');
    int questionPos = commandPart.indexOf('?');

    QString cmdName;
    QString paramPart;

    if (questionPos != -1 && (equalPos == -1 || questionPos < equalPos)) {
        // 查询指令 AT+CMD?
        if (questionPos == commandPart.length() - 1) {
            cmdName = commandPart.left(questionPos);
            m_type = AtCommandType::Query;
        } else {
            return false; // 格式错误
        }
    } else if (equalPos != -1) {
        // 设置指令 AT+CMD=... 或测试指令 AT+CMD=?
        cmdName = commandPart.left(equalPos);
        paramPart = commandPart.mid(equalPos + 1);

        if (paramPart == "?") {
            m_type = AtCommandType::Test;
        } else {
            m_type = AtCommandType::Set;
            parseParameters(paramPart);
        }
    } else {
        // 执行指令 AT+CMD
        cmdName = commandPart;
        m_type = AtCommandType::Execute;
    }

    // 获取指令枚举
    m_command = getCommandFromString(cmdName);

    return m_command != MqttAtCommand::Unknown;
}

QString AtCommand::format() const
{
    if (m_command == MqttAtCommand::Unknown) {
        return QString();
    }

    if (m_command == MqttAtCommand::AT) {
        return "AT";
    }

    QString result = "AT+" + getCommandString(m_command);

    switch (m_type) {
    case AtCommandType::Query:
        result += "?";
        break;
    case AtCommandType::Test:
        result += "=?";
        break;
    case AtCommandType::Set:
        if (!m_parameters.isEmpty()) {
            result += "=" + m_parameters.join(",");
        }
        break;
    case AtCommandType::Execute:
        // 执行指令不需要额外内容
        break;
    default:
        break;
    }

    return result;
}

bool AtCommand::isValid() const
{
    return m_command != MqttAtCommand::Unknown && m_type != AtCommandType::Unknown;
}

QString AtCommand::getParameter(int index) const
{
    if (index >= 0 && index < m_parameters.size()) {
        return m_parameters.at(index);
    }
    return QString();
}

QString AtCommand::getCommandString(MqttAtCommand command)
{
    switch (command) {
    case MqttAtCommand::AT:         return "AT";
    case MqttAtCommand::QMTOPEN:    return "QMTOPEN";
    case MqttAtCommand::QMTCLOSE:   return "QMTCLOSE";
    case MqttAtCommand::QMTCONN:    return "QMTCONN";
    case MqttAtCommand::QMTDISC:    return "QMTDISC";
    case MqttAtCommand::QMTSUB:     return "QMTSUB";
    case MqttAtCommand::QMTUNSUB:   return "QMTUNSUB";
    case MqttAtCommand::QMTPUB:     return "QMTPUB";
    case MqttAtCommand::QMTPUBEX:   return "QMTPUBEX";
    case MqttAtCommand::QMTCFG:     return "QMTCFG";
    default:                        return "UNKNOWN";
    }
}

MqttAtCommand AtCommand::getCommandFromString(const QString& commandStr)
{
    QString cmd = commandStr.toUpper();

    if (cmd == "AT")        return MqttAtCommand::AT;
    if (cmd == "QMTOPEN")   return MqttAtCommand::QMTOPEN;
    if (cmd == "QMTCLOSE")  return MqttAtCommand::QMTCLOSE;
    if (cmd == "QMTCONN")   return MqttAtCommand::QMTCONN;
    if (cmd == "QMTDISC")   return MqttAtCommand::QMTDISC;
    if (cmd == "QMTSUB")    return MqttAtCommand::QMTSUB;
    if (cmd == "QMTUNSUB")  return MqttAtCommand::QMTUNSUB;
    if (cmd == "QMTPUB")    return MqttAtCommand::QMTPUB;
    if (cmd == "QMTPUBEX")  return MqttAtCommand::QMTPUBEX;
    if (cmd == "QMTCFG")    return MqttAtCommand::QMTCFG;

    return MqttAtCommand::Unknown;
}

void AtCommand::parseParameters(const QString& paramPart)
{
    if (paramPart.isEmpty()) {
        return;
    }

    // 简单的参数解析，支持逗号分隔和引号包围
    QStringList params;
    QString current;
    bool inQuotes = false;

    for (int i = 0; i < paramPart.length(); ++i) {
        QChar ch = paramPart.at(i);

        if (ch == '"') {
            inQuotes = !inQuotes;
            current += ch;
        } else if (ch == ',' && !inQuotes) {
            params.append(removeQuotes(current.trimmed()));
            current.clear();
        } else {
            current += ch;
        }
    }

    // 添加最后一个参数
    if (!current.isEmpty()) {
        params.append(removeQuotes(current.trimmed()));
    }

    m_parameters = params;
}

QString AtCommand::removeQuotes(const QString& str) const
{
    QString result = str;
    if (result.startsWith('"') && result.endsWith('"') && result.length() >= 2) {
        result = result.mid(1, result.length() - 2);
    }
    return result;
}
